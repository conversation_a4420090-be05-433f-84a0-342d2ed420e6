#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试签到积分设置功能
"""

import requests
import json

BASE_URL = "http://localhost:7799"

def test_checkin_settings():
    """测试签到积分设置功能"""
    session = requests.Session()
    
    print("=== 测试签到积分设置功能 ===")
    
    # 1. 登录管理员账户
    print("\n1. 登录管理员账户...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = session.post(f"{BASE_URL}/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"✓ 登录成功: {result.get('message')}")
        else:
            print(f"✗ 登录失败: {result.get('message')}")
            return
    else:
        print(f"✗ 登录请求失败: {response.status_code}")
        return
    
    # 2. 获取当前设置
    print("\n2. 获取当前设置...")
    response = session.get(f"{BASE_URL}/admin/settings")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            settings = result.get('settings', {})
            current_daily_bonus = settings.get('daily_bonus', 100)
            print(f"✓ 当前签到积分设置: {current_daily_bonus}")
        else:
            print(f"✗ 获取设置失败: {result.get('message')}")
            return
    else:
        print(f"✗ 获取设置请求失败: {response.status_code}")
        return
    
    # 3. 更新签到积分设置
    print("\n3. 更新签到积分设置...")
    new_daily_bonus = 50  # 测试设置为50积分
    update_data = {
        "daily_bonus": new_daily_bonus
    }
    
    response = session.post(f"{BASE_URL}/admin/settings", json=update_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"✓ 设置更新成功: {result.get('message')}")
        else:
            print(f"✗ 设置更新失败: {result.get('message')}")
            return
    else:
        print(f"✗ 设置更新请求失败: {response.status_code}")
        return
    
    # 4. 验证设置是否生效
    print("\n4. 验证设置是否生效...")
    response = session.get(f"{BASE_URL}/admin/settings")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            settings = result.get('settings', {})
            updated_daily_bonus = settings.get('daily_bonus', 100)
            if updated_daily_bonus == new_daily_bonus:
                print(f"✓ 设置验证成功: 签到积分已更新为 {updated_daily_bonus}")
            else:
                print(f"✗ 设置验证失败: 期望 {new_daily_bonus}，实际 {updated_daily_bonus}")
                return
        else:
            print(f"✗ 验证设置失败: {result.get('message')}")
            return
    else:
        print(f"✗ 验证设置请求失败: {response.status_code}")
        return
    
    # 5. 恢复原始设置
    print("\n5. 恢复原始设置...")
    restore_data = {
        "daily_bonus": current_daily_bonus
    }
    
    response = session.post(f"{BASE_URL}/admin/settings", json=restore_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"✓ 设置恢复成功: 签到积分恢复为 {current_daily_bonus}")
        else:
            print(f"✗ 设置恢复失败: {result.get('message')}")
    else:
        print(f"✗ 设置恢复请求失败: {response.status_code}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_checkin_settings()
