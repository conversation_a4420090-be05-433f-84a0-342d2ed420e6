#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试签到功能是否使用新的积分设置
"""

import json
from points_system import PointsSystem

def test_checkin_points():
    """测试签到积分设置"""
    print("=== 测试签到积分设置 ===")
    
    # 1. 创建积分系统实例
    points_system = PointsSystem()
    
    # 2. 获取当前设置
    settings = points_system.get_settings()
    daily_bonus = settings.get('daily_bonus', 100)
    print(f"当前签到积分设置: {daily_bonus}")
    
    # 3. 测试更新设置
    print("\n测试更新签到积分设置...")
    new_daily_bonus = 75
    success = points_system.update_settings({'daily_bonus': new_daily_bonus})
    
    if success:
        print(f"✓ 设置更新成功")
        
        # 验证设置是否生效
        updated_settings = points_system.get_settings()
        actual_daily_bonus = updated_settings.get('daily_bonus', 100)
        
        if actual_daily_bonus == new_daily_bonus:
            print(f"✓ 设置验证成功: 签到积分已更新为 {actual_daily_bonus}")
        else:
            print(f"✗ 设置验证失败: 期望 {new_daily_bonus}，实际 {actual_daily_bonus}")
    else:
        print("✗ 设置更新失败")
    
    # 4. 恢复原始设置
    print(f"\n恢复原始设置: {daily_bonus}")
    points_system.update_settings({'daily_bonus': daily_bonus})
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_checkin_points()
